<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="请领人" prop="getPrincipal">
        <el-input
          v-model="queryParams.getPrincipal"
          placeholder="请输入请领人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:facilityGetStorage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:facilityGetStorage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:facilityGetStorage:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="facilityGetStorageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="请领人" align="center" prop="getPrincipal" />
      <el-table-column label="描述" align="center" prop="details" />
      <el-table-column label="请领时间" align="center" prop="drawTime" width="180"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:facilityGetStorage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:facilityGetStorage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备请领对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="请领人" prop="getPrincipal">
          <el-input v-model="form.getPrincipal" placeholder="请输入请领人" />
        </el-form-item>
        <el-form-item label="描述" prop="details">
          <el-input v-model="form.details" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="请领时间" prop="drawTime">
          <el-date-picker clearable
                          v-model="form.drawTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请选择请领时间">
          </el-date-picker>
        </el-form-item>

        <!-- 设备选择区域 -->
        <el-form-item label="选择设备" prop="facilityDetails">
          <div class="facility-selection">
            <!-- 设备库存列表 -->
            <div class="facility-list">
              <el-table
                :data="facilityStorageList"
                style="width: 100%"
                max-height="300"
                @selection-change="handleFacilitySelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="设备名称" prop="facilityName" width="150" />
                <el-table-column label="当前库存" prop="stock" width="100" align="center">
                  <template slot-scope="scope">
                    <el-tag :type="Number(scope.row.stock) > 0 ? 'success' : 'danger'">
                      {{ scope.row.stock }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="请领数量" width="120" align="center">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.requestCount"
                      :min="0"
                      :max="Number(scope.row.stock)"
                      :disabled="Number(scope.row.stock) <= 0"
                      size="mini"
                      @change="updateFacilityRequest(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 已选设备汇总 -->
            <div class="selected-facilities" v-if="selectedFacilities.length > 0">
              <h4>已选设备汇总：</h4>
              <el-table :data="selectedFacilities" style="width: 100%">
                <el-table-column label="设备名称" prop="facilityName" />
                <el-table-column label="请领数量" prop="requestCount" width="100" align="center" />
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="mini"
                      @click="removeFacilityRequest(scope.row)"
                      style="color: #f56c6c;">
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFacilityGetStorage, getFacilityGetStorage, delFacilityGetStorage, addFacilityGetStorage, updateFacilityGetStorage } from "@/api/biz/facilityGetStorage";
import { facilityOption } from "@/api/biz/facilityList";
import { listFacilityStorage } from "@/api/biz/facilityStorage";

export default {
  name: "FacilityGetStorage",
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 设备清单选项
      facilityOptions: [],
      // 设备库存列表
      facilityStorageList: [],
      // 已选择的设备列表
      selectedFacilities: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备请领表格数据
      facilityGetStorageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        getPrincipal: undefined,
        details: undefined,
        drawTime: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        getPrincipal: [
          { required: true, message: "请领人不能为空", trigger: "blur" }
        ],
        details: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ],
        drawTime: [
          { required: true, message: "请领时间不能为空", trigger: "blur" }
        ],
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getFacilityOption();
  },
  methods: {
    // 设备清单选项
    getFacilityOption(){
      facilityOption({
        projectId: this.projectId
      }).then(res => {
        this.facilityOptions = res.data;
      })
    },
    // 获取设备库存列表
    getFacilityStorageList() {
      listFacilityStorage({
        projectId: this.projectId,
        pageNum: 1,
        pageSize: 1000 // 获取所有设备库存
      }).then(response => {
        this.facilityStorageList = response.rows.map(item => ({
          ...item,
          stock: Number(item.stock) || 0, // 确保库存为数字类型
          requestCount: 0 // 初始化请领数量为0
        }));
      });
    },
    /** 查询设备请领列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId;
      listFacilityGetStorage(this.queryParams).then(response => {
        this.facilityGetStorageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        getPrincipal: undefined,
        details: undefined,
        drawTime: undefined,
        projectId: undefined,
        facilityIds: [],
        facilityDetails: [],
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      // 重置设备选择相关数据
      this.selectedFacilities = [];
      this.facilityStorageList.forEach(item => {
        item.requestCount = 0;
      });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getFacilityStorageList(); // 获取设备库存列表
      this.open = true;
      this.title = "添加设备请领";
    },
    // 处理设备选择变更
    handleFacilitySelectionChange(selection) {
      // 更新选中状态，但保持已有的请领数量
      this.facilityStorageList.forEach(item => {
        const isSelected = selection.some(selected => selected.id === item.id);
        if (!isSelected) {
          item.requestCount = 0;
        }
      });
      this.updateSelectedFacilities();
    },
    // 更新设备请领数量
    updateFacilityRequest(facility) {
      const stock = Number(facility.stock) || 0;
      const requestCount = Number(facility.requestCount) || 0;

      if (requestCount > 0 && requestCount <= stock) {
        this.updateSelectedFacilities();
      } else if (requestCount <= 0) {
        facility.requestCount = 0;
        this.updateSelectedFacilities();
      } else if (requestCount > stock) {
        // 如果超过库存，自动调整为最大库存数量
        facility.requestCount = stock;
        this.updateSelectedFacilities();
        this.$modal.msgWarning(`请领数量不能超过库存数量 ${stock}`);
      }
    },
    // 更新已选设备列表
    updateSelectedFacilities() {
      this.selectedFacilities = this.facilityStorageList
        .filter(item => item.requestCount > 0)
        .map(item => ({
          facilityId: item.facilityId,
          facilityName: item.facilityName,
          requestCount: item.requestCount,
          stock: item.stock
        }));

      // 更新表单数据
      this.form.facilityIds = this.selectedFacilities.map(item => item.facilityId);
      this.form.facilityDetails = this.selectedFacilities.map(item => ({
        facilityId: item.facilityId,
        count: item.requestCount
      }));
    },
    // 移除设备请领
    removeFacilityRequest(facility) {
      const storageItem = this.facilityStorageList.find(item => item.facilityId === facility.facilityId);
      if (storageItem) {
        storageItem.requestCount = 0;
      }
      this.updateSelectedFacilities();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      this.getFacilityStorageList(); // 先获取设备库存列表

      const id = row.id || this.ids
      getFacilityGetStorage(id).then(response => {
        this.loading = false;
        this.form = response.data;

        // 如果有设备详情数据，恢复选择状态
        if (this.form.facilityDetails && this.form.facilityDetails.length > 0) {
          this.$nextTick(() => {
            this.form.facilityDetails.forEach(detail => {
              const storageItem = this.facilityStorageList.find(item => item.facilityId === detail.facilityId);
              if (storageItem) {
                storageItem.requestCount = detail.count || 0;
              }
            });
            this.updateSelectedFacilities();
          });
        }

        this.open = true;
        this.title = "修改设备请领";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 验证是否选择了设备
      if (this.selectedFacilities.length === 0) {
        this.$modal.msgError("请至少选择一个设备进行请领");
        return;
      }

      // 验证请领数量
      const invalidFacilities = this.selectedFacilities.filter(item => {
        const requestCount = Number(item.requestCount) || 0;
        const stock = Number(item.stock) || 0;
        return requestCount <= 0 || requestCount > stock;
      });
      if (invalidFacilities.length > 0) {
        this.$modal.msgError("请检查设备请领数量，不能超过库存或小于等于0");
        return;
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId;

          // 确保设备详情数据正确
          this.form.facilityDetails = this.selectedFacilities.map(item => ({
            facilityId: item.facilityId,
            count: item.requestCount
          }));

          if (this.form.id != null) {
            updateFacilityGetStorage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addFacilityGetStorage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除设备请领编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delFacilityGetStorage(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped>
.facility-selection {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.facility-list {
  margin-bottom: 20px;
}

.selected-facilities {
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
}

.selected-facilities h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.el-input-number {
  width: 100px;
}

.el-tag {
  font-weight: 500;
}
</style>
